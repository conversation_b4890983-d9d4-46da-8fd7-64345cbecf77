<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Video Detection - Web Interface</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow-x: hidden;
        }
        
        .header {
            background: rgba(0,0,0,0.2);
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px;
        }
        
        .status.connected { background: #28a745; }
        .status.disconnected { background: #dc3545; }
        .status.connecting { background: #ffc107; color: #000; }
        
        .main-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            min-height: calc(100vh - 120px);
        }
        
        .video-section {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .video-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #video-feed {
            width: 100%;
            height: auto;
            max-height: 500px;
            object-fit: cover;
        }
        
        .video-placeholder {
            text-align: center;
            color: #888;
            padding: 60px 20px;
        }
        
        .video-placeholder i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: white;
        }
        
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.3); }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; transform: none; }
        
        .btn-primary { background: #007bff; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-secondary { background: #6c757d; }
        
        .detection-panels {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .panel h3 {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .panel-content {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .detection-item {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        
        .detection-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .detection-confidence {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #00d4ff;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .no-results {
            text-align: center;
            padding: 30px;
            opacity: 0.6;
        }
        
        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
            }
            .controls {
                justify-content: center;
            }
            .btn {
                padding: 10px 16px;
                font-size: 14px;
            }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-video"></i> AI Video Detection</h1>
        <div id="connection-status" class="status connecting">
            <i class="fas fa-circle"></i> Connecting...
        </div>
    </div>

    <div class="main-container">
        <div class="video-section">
            <div class="video-container">
                <video id="video-feed" style="display: none;" autoplay muted playsinline></video>
                <div id="video-placeholder" class="video-placeholder">
                    <i class="fas fa-video"></i>
                    <h3>Camera Feed</h3>
                    <p>Click "Start Detection" to begin</p>
                </div>
            </div>
            
            <div class="controls">
                <button id="start-btn" class="btn btn-success">
                    <i class="fas fa-play"></i> Start Detection
                </button>
                <button id="stop-btn" class="btn btn-danger" disabled>
                    <i class="fas fa-stop"></i> Stop Detection
                </button>
                <button id="snapshot-btn" class="btn btn-secondary">
                    <i class="fas fa-camera"></i> Snapshot
                </button>
                <button id="camera-btn" class="btn btn-primary">
                    <i class="fas fa-video"></i> Start Camera
                </button>
            </div>
        </div>

        <div class="detection-panels">
            <div class="panel">
                <h3><i class="fas fa-smile"></i> Facial Expression</h3>
                <div id="facial-results" class="panel-content">
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <p>No detections yet</p>
                    </div>
                </div>
            </div>

            <div class="panel">
                <h3><i class="fas fa-user-clock"></i> Age Detection</h3>
                <div id="age-results" class="panel-content">
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <p>No detections yet</p>
                    </div>
                </div>
            </div>

            <div class="panel">
                <h3><i class="fas fa-cube"></i> Object Detection</h3>
                <div id="object-results" class="panel-content">
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <p>No detections yet</p>
                    </div>
                </div>
            </div>

            <div class="panel">
                <h3><i class="fas fa-chart-bar"></i> Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div id="total-detections" class="stat-value">0</div>
                        <div class="stat-label">Total Detections</div>
                    </div>
                    <div class="stat-item">
                        <div id="session-time" class="stat-value">00:00</div>
                        <div class="stat-label">Session Time</div>
                    </div>
                    <div class="stat-item">
                        <div id="fps-counter" class="stat-value">0</div>
                        <div class="stat-label">FPS</div>
                    </div>
                    <div class="stat-item">
                        <div id="detection-count" class="stat-value">0</div>
                        <div class="stat-label">Live Count</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script>
        const API_BASE = 'http://localhost:8000';
        let socket = null;
        let isDetectionRunning = false;
        let sessionStartTime = null;
        let sessionTimer = null;
        let stream = null;

        // Initialize on page load
        window.addEventListener('load', () => {
            initializeApp();
        });

        async function initializeApp() {
            console.log('🚀 Initializing AI Video Detection Web Interface...');
            
            // Test backend connection
            await testConnection();
            
            // Setup event listeners
            setupEventListeners();
            
            // Connect WebSocket
            connectWebSocket();
            
            console.log('✅ App initialized');
        }

        async function testConnection() {
            const statusEl = document.getElementById('connection-status');
            
            try {
                statusEl.className = 'status connecting';
                statusEl.innerHTML = '<i class="fas fa-circle"></i> Connecting...';
                
                const response = await fetch(`${API_BASE}/api/status`);
                const data = await response.json();
                
                if (response.ok) {
                    statusEl.className = 'status connected';
                    statusEl.innerHTML = '<i class="fas fa-circle"></i> Connected';
                    console.log('✅ Backend connected:', data);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.className = 'status disconnected';
                statusEl.innerHTML = '<i class="fas fa-circle"></i> Disconnected';
                console.error('❌ Connection failed:', error);
            }
        }

        function setupEventListeners() {
            document.getElementById('start-btn').addEventListener('click', startDetection);
            document.getElementById('stop-btn').addEventListener('click', stopDetection);
            document.getElementById('snapshot-btn').addEventListener('click', takeSnapshot);
            document.getElementById('camera-btn').addEventListener('click', startCamera);
        }

        function connectWebSocket() {
            try {
                socket = io(API_BASE);
                
                socket.on('connect', () => {
                    console.log('🔌 WebSocket connected');
                });
                
                socket.on('detection_update', (data) => {
                    updateDetectionResults(data);
                });
                
                socket.on('detection_started', () => {
                    console.log('▶️ Detection started');
                });
                
                socket.on('detection_stopped', () => {
                    console.log('⏹️ Detection stopped');
                });
                
            } catch (error) {
                console.error('❌ WebSocket error:', error);
            }
        }

        async function startCamera() {
            try {
                console.log('📷 Starting camera...');
                
                stream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 1280, height: 720 },
                    audio: false
                });
                
                const video = document.getElementById('video-feed');
                const placeholder = document.getElementById('video-placeholder');
                
                video.srcObject = stream;
                video.style.display = 'block';
                placeholder.style.display = 'none';
                
                console.log('✅ Camera started');
                
            } catch (error) {
                console.error('❌ Camera error:', error);
                alert('Camera access failed. Please allow camera permissions and try again.');
            }
        }

        async function startDetection() {
            try {
                console.log('🚀 Starting detection...');
                
                // Start camera if not already running
                if (!stream) {
                    await startCamera();
                }
                
                const response = await fetch(`${API_BASE}/api/detection/start`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    isDetectionRunning = true;
                    sessionStartTime = new Date();
                    updateControlsState();
                    startSessionTimer();
                    console.log('✅ Detection started:', data);
                } else {
                    throw new Error(data.error || 'Failed to start detection');
                }
                
            } catch (error) {
                console.error('❌ Start detection failed:', error);
                alert(`Failed to start detection: ${error.message}`);
            }
        }

        async function stopDetection() {
            try {
                console.log('🛑 Stopping detection...');
                
                const response = await fetch(`${API_BASE}/api/detection/stop`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    isDetectionRunning = false;
                    sessionStartTime = null;
                    updateControlsState();
                    stopSessionTimer();
                    console.log('✅ Detection stopped:', data);
                } else {
                    throw new Error(data.error || 'Failed to stop detection');
                }
                
            } catch (error) {
                console.error('❌ Stop detection failed:', error);
                alert(`Failed to stop detection: ${error.message}`);
            }
        }

        function takeSnapshot() {
            const video = document.getElementById('video-feed');
            if (!video.srcObject) {
                alert('Please start the camera first');
                return;
            }
            
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            canvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `snapshot_${new Date().toISOString().replace(/[:.]/g, '-')}.png`;
                a.click();
                URL.revokeObjectURL(url);
                console.log('📸 Snapshot saved');
            });
        }

        function updateControlsState() {
            const startBtn = document.getElementById('start-btn');
            const stopBtn = document.getElementById('stop-btn');
            
            startBtn.disabled = isDetectionRunning;
            stopBtn.disabled = !isDetectionRunning;
            
            if (isDetectionRunning) {
                startBtn.innerHTML = '<div class="loading"></div> Running...';
            } else {
                startBtn.innerHTML = '<i class="fas fa-play"></i> Start Detection';
            }
        }

        function startSessionTimer() {
            sessionTimer = setInterval(() => {
                if (sessionStartTime) {
                    const elapsed = new Date() - sessionStartTime;
                    const minutes = Math.floor(elapsed / 60000);
                    const seconds = Math.floor((elapsed % 60000) / 1000);
                    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    document.getElementById('session-time').textContent = timeString;
                }
            }, 1000);
        }

        function stopSessionTimer() {
            if (sessionTimer) {
                clearInterval(sessionTimer);
                sessionTimer = null;
            }
        }

        function updateDetectionResults(data) {
            // Update facial expression results
            updatePanel('facial-results', data.facial?.detections || []);
            
            // Update age detection results
            updatePanel('age-results', data.age?.detections || []);
            
            // Update object detection results
            updatePanel('object-results', data.object?.detections || []);
            
            // Update statistics
            const totalDetections = (data.facial?.detections?.length || 0) +
                                  (data.age?.detections?.length || 0) +
                                  (data.object?.detections?.length || 0);
            
            document.getElementById('detection-count').textContent = totalDetections;
            
            if (data.facial?.fps) {
                document.getElementById('fps-counter').textContent = Math.round(data.facial.fps);
            }
        }

        function updatePanel(panelId, detections) {
            const panel = document.getElementById(panelId);
            
            if (!detections || detections.length === 0) {
                panel.innerHTML = `
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <p>No detections</p>
                    </div>
                `;
                return;
            }
            
            panel.innerHTML = detections.map(detection => `
                <div class="detection-item">
                    <div class="detection-label">${detection.label}</div>
                    <div class="detection-confidence">
                        Confidence: ${Math.round((detection.confidence || 0) * 100)}%
                    </div>
                </div>
            `).join('');
        }

        // Auto-refresh connection status
        setInterval(testConnection, 30000);
    </script>
</body>
</html>
