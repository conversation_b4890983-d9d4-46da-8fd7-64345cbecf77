<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Video Detection - Backend Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .response {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI Video Detection Backend Test</h1>
        
        <div id="connection-status" class="status info">
            🔄 Testing connection to backend...
        </div>

        <div class="controls">
            <button onclick="testConnection()">🔌 Test Connection</button>
            <button onclick="startDetection()">▶️ Start Detection</button>
            <button onclick="stopDetection()">⏹️ Stop Detection</button>
            <button onclick="getResults()">📊 Get Results</button>
            <button onclick="testCamera()">📷 Test Camera</button>
        </div>

        <div class="test-section">
            <h3>📡 API Response</h3>
            <div id="response" class="response">Click a button to test the API...</div>
        </div>

        <div class="test-section">
            <h3>🔄 Real-time Updates</h3>
            <div id="realtime-status" class="status info">WebSocket not connected</div>
            <div id="realtime-data" class="response">Waiting for real-time data...</div>
        </div>

        <div class="test-section">
            <h3>ℹ️ Instructions</h3>
            <p><strong>This page tests your Python backend while you set up the Electron frontend.</strong></p>
            <ol>
                <li>Make sure your Python backend is running: <code>python electron-frontend/backend-bridge/server.py</code></li>
                <li>Click "Test Connection" to verify the backend is working</li>
                <li>Try "Start Detection" to test the AI modules</li>
                <li>Use "Get Results" to see detection data</li>
                <li>Install Node.js and run the full Electron app when ready</li>
            </ol>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script>
        const API_BASE = 'http://localhost:8000';
        let socket = null;

        // Test connection on page load
        window.addEventListener('load', () => {
            testConnection();
            connectWebSocket();
        });

        async function testConnection() {
            const statusEl = document.getElementById('connection-status');
            const responseEl = document.getElementById('response');
            
            try {
                statusEl.className = 'status info';
                statusEl.textContent = '🔄 Testing connection...';
                
                const response = await fetch(`${API_BASE}/api/status`);
                const data = await response.json();
                
                if (response.ok) {
                    statusEl.className = 'status success';
                    statusEl.textContent = '✅ Backend connected successfully!';
                    responseEl.textContent = JSON.stringify(data, null, 2);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ Connection failed: ${error.message}`;
                responseEl.textContent = `Error: ${error.message}\n\nMake sure the Python backend is running:\npython electron-frontend/backend-bridge/server.py`;
            }
        }

        async function startDetection() {
            await apiCall('/api/detection/start', 'POST');
        }

        async function stopDetection() {
            await apiCall('/api/detection/stop', 'POST');
        }

        async function getResults() {
            await apiCall('/api/detection/results', 'GET');
        }

        async function testCamera() {
            await apiCall('/api/camera/test', 'POST');
        }

        async function apiCall(endpoint, method = 'GET', data = null) {
            const responseEl = document.getElementById('response');
            
            try {
                responseEl.textContent = `🔄 ${method} ${endpoint}...`;
                
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                
                if (response.ok) {
                    responseEl.textContent = `✅ ${method} ${endpoint}\n\n${JSON.stringify(result, null, 2)}`;
                } else {
                    responseEl.textContent = `❌ ${method} ${endpoint}\n\nError: ${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                responseEl.textContent = `❌ ${method} ${endpoint}\n\nError: ${error.message}`;
            }
        }

        function connectWebSocket() {
            const realtimeStatus = document.getElementById('realtime-status');
            const realtimeData = document.getElementById('realtime-data');
            
            try {
                socket = io(API_BASE);
                
                socket.on('connect', () => {
                    realtimeStatus.className = 'status success';
                    realtimeStatus.textContent = '✅ WebSocket connected';
                    realtimeData.textContent = 'Connected! Waiting for detection updates...';
                });
                
                socket.on('disconnect', () => {
                    realtimeStatus.className = 'status error';
                    realtimeStatus.textContent = '❌ WebSocket disconnected';
                });
                
                socket.on('detection_update', (data) => {
                    realtimeData.textContent = `🔄 Detection Update (${new Date().toLocaleTimeString()})\n\n${JSON.stringify(data, null, 2)}`;
                });
                
                socket.on('detection_started', (data) => {
                    realtimeData.textContent = `▶️ Detection Started (${new Date().toLocaleTimeString()})\n\n${JSON.stringify(data, null, 2)}`;
                });
                
                socket.on('detection_stopped', (data) => {
                    realtimeData.textContent = `⏹️ Detection Stopped (${new Date().toLocaleTimeString()})\n\n${JSON.stringify(data, null, 2)}`;
                });
                
                socket.on('status', (data) => {
                    realtimeData.textContent = `📡 Status Update (${new Date().toLocaleTimeString()})\n\n${JSON.stringify(data, null, 2)}`;
                });
                
            } catch (error) {
                realtimeStatus.className = 'status error';
                realtimeStatus.textContent = `❌ WebSocket error: ${error.message}`;
            }
        }

        // Auto-refresh connection status every 30 seconds
        setInterval(testConnection, 30000);
    </script>
</body>
</html>
