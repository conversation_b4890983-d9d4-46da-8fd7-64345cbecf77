@echo off
echo ========================================
echo   Installing Dependencies
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed!
    echo.
    echo Please install Python from: https://python.org/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python is installed:
python --version
echo.

REM Install Python dependencies
echo 📦 Installing Python dependencies...
echo.

echo Installing Flask (web framework)...
pip install flask

echo Installing Flask-CORS (cross-origin requests)...
pip install flask-cors

echo Installing Flask-SocketIO (real-time communication)...
pip install flask-socketio

echo Installing Python-SocketIO (WebSocket support)...
pip install python-socketio

echo Installing Eventlet (async support)...
pip install eventlet

echo.
echo ✅ Python dependencies installed successfully!
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Node.js is not installed!
    echo.
    echo To complete the setup, please:
    echo 1. Download Node.js from: https://nodejs.org/
    echo 2. Install the LTS version
    echo 3. Restart your computer
    echo 4. Run START_ELECTRON_APP.bat
    echo.
) else (
    echo ✅ Node.js is installed:
    node --version
    echo.
    
    echo 📦 Installing Node.js dependencies...
    cd electron-frontend
    
    if exist "package.json" (
        call npm install
        if %errorlevel% equ 0 (
            echo ✅ Node.js dependencies installed successfully!
            echo.
            echo 🎉 All dependencies are now installed!
            echo.
            echo To start the application, run: START_ELECTRON_APP.bat
        ) else (
            echo ⚠️ Some Node.js packages failed to install
            echo Try running: npm install --force
        )
    ) else (
        echo ❌ package.json not found in electron-frontend directory
    )
    
    cd ..
)

echo.
echo Installation complete!
echo.
pause
