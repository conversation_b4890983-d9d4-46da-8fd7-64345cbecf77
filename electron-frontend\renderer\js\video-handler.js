/**
 * AI Video Detection - Video Handler
 * Manages video feed, camera access, and video display
 */

class VideoHandler {
    constructor() {
        this.videoElement = null;
        this.canvasElement = null;
        this.stream = null;
        this.isInitialized = false;
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        
        // Video settings
        this.videoConstraints = {
            width: { ideal: 1280, max: 1920 },
            height: { ideal: 720, max: 1080 },
            frameRate: { ideal: 30, max: 60 }
        };
        
        // Bind methods
        this.init = this.init.bind(this);
        this.startCamera = this.startCamera.bind(this);
        this.stopCamera = this.stopCamera.bind(this);
        this.takeSnapshot = this.takeSnapshot.bind(this);
        this.startRecording = this.startRecording.bind(this);
        this.stopRecording = this.stopRecording.bind(this);
    }

    async init() {
        try {
            console.log('🎥 Initializing video handler...');
            
            // Get video elements
            this.videoElement = document.getElementById('video-feed');
            this.canvasElement = document.getElementById('detection-overlay');
            
            if (!this.videoElement) {
                throw new Error('Video element not found');
            }
            
            if (!this.canvasElement) {
                throw new Error('Canvas element not found');
            }
            
            // Setup canvas context
            this.canvasContext = this.canvasElement.getContext('2d');
            
            // Setup video event listeners
            this.setupVideoEventListeners();
            
            // Try to start camera
            await this.startCamera();
            
            this.isInitialized = true;
            console.log('✅ Video handler initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize video handler:', error);
            this.showVideoError(error.message);
            throw error;
        }
    }

    setupVideoEventListeners() {
        if (!this.videoElement) return;

        this.videoElement.addEventListener('loadedmetadata', () => {
            console.log('📹 Video metadata loaded');
            this.updateVideoInfo();
            this.resizeCanvas();
        });

        this.videoElement.addEventListener('play', () => {
            console.log('▶️ Video playback started');
            this.startFPSCounter();
        });

        this.videoElement.addEventListener('pause', () => {
            console.log('⏸️ Video playback paused');
            this.stopFPSCounter();
        });

        this.videoElement.addEventListener('error', (e) => {
            console.error('❌ Video error:', e);
            this.showVideoError('Video playback error');
        });

        // Handle window resize
        window.addEventListener('resize', Utils.performance.debounce(() => {
            this.handleResize();
        }, 250));
    }

    async startCamera() {
        try {
            console.log('📷 Starting camera...');
            
            // Check if camera is already running
            if (this.stream) {
                console.log('📷 Camera already running');
                return;
            }

            // Request camera permission
            const constraints = {
                video: this.videoConstraints,
                audio: false // We don't need audio for detection
            };

            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            if (this.videoElement) {
                this.videoElement.srcObject = this.stream;
                await this.videoElement.play();
            }

            console.log('✅ Camera started successfully');
            this.updateConnectionStatus(true);
            
            // Update UI
            this.showVideoPlaceholder(false);
            
        } catch (error) {
            console.error('❌ Failed to start camera:', error);
            this.handleCameraError(error);
            throw error;
        }
    }

    stopCamera() {
        try {
            console.log('🛑 Stopping camera...');
            
            if (this.stream) {
                this.stream.getTracks().forEach(track => {
                    track.stop();
                });
                this.stream = null;
            }

            if (this.videoElement) {
                this.videoElement.srcObject = null;
            }

            this.stopFPSCounter();
            this.updateConnectionStatus(false);
            this.showVideoPlaceholder(true);
            
            console.log('✅ Camera stopped');
            
        } catch (error) {
            console.error('❌ Error stopping camera:', error);
        }
    }

    async takeSnapshot() {
        try {
            if (!this.videoElement || !this.canvasElement) {
                throw new Error('Video or canvas element not available');
            }

            // Create a temporary canvas for snapshot
            const snapshotCanvas = document.createElement('canvas');
            const snapshotContext = snapshotCanvas.getContext('2d');
            
            // Set canvas size to video size
            snapshotCanvas.width = this.videoElement.videoWidth;
            snapshotCanvas.height = this.videoElement.videoHeight;
            
            // Draw video frame to canvas
            snapshotContext.drawImage(this.videoElement, 0, 0);
            
            // Convert to blob
            const blob = await new Promise(resolve => {
                snapshotCanvas.toBlob(resolve, 'image/png');
            });
            
            // Create download link
            const url = URL.createObjectURL(blob);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `snapshot_${timestamp}.png`;
            
            // Trigger download
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log(`📸 Snapshot saved: ${filename}`);
            return filename;
            
        } catch (error) {
            console.error('❌ Failed to take snapshot:', error);
            throw error;
        }
    }

    async startRecording() {
        try {
            if (!this.stream) {
                throw new Error('No video stream available');
            }

            if (this.isRecording) {
                console.log('🎥 Already recording');
                return;
            }

            console.log('🎥 Starting recording...');
            
            this.recordedChunks = [];
            
            // Create media recorder
            const options = {
                mimeType: 'video/webm;codecs=vp9',
                videoBitsPerSecond: 2500000 // 2.5 Mbps
            };
            
            this.mediaRecorder = new MediaRecorder(this.stream, options);
            
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };
            
            this.mediaRecorder.onstop = () => {
                this.saveRecording();
            };
            
            this.mediaRecorder.start(1000); // Collect data every second
            this.isRecording = true;
            
            console.log('✅ Recording started');
            
        } catch (error) {
            console.error('❌ Failed to start recording:', error);
            throw error;
        }
    }

    stopRecording() {
        try {
            if (!this.isRecording || !this.mediaRecorder) {
                console.log('🎥 Not currently recording');
                return;
            }

            console.log('🛑 Stopping recording...');
            
            this.mediaRecorder.stop();
            this.isRecording = false;
            
            console.log('✅ Recording stopped');
            
        } catch (error) {
            console.error('❌ Failed to stop recording:', error);
        }
    }

    saveRecording() {
        try {
            if (this.recordedChunks.length === 0) {
                console.warn('⚠️ No recorded data to save');
                return;
            }

            const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
            const url = URL.createObjectURL(blob);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `recording_${timestamp}.webm`;
            
            // Trigger download
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log(`🎥 Recording saved: ${filename}`);
            this.recordedChunks = [];
            
        } catch (error) {
            console.error('❌ Failed to save recording:', error);
        }
    }

    async loadFile(filePath) {
        try {
            console.log(`📁 Loading file: ${filePath}`);
            
            // Stop camera if running
            this.stopCamera();
            
            // Load file into video element
            if (this.videoElement) {
                this.videoElement.src = filePath;
                await this.videoElement.load();
            }
            
            console.log('✅ File loaded successfully');
            
        } catch (error) {
            console.error('❌ Failed to load file:', error);
            throw error;
        }
    }

    updateVideoInfo() {
        if (!this.videoElement) return;

        const resolution = `${this.videoElement.videoWidth}x${this.videoElement.videoHeight}`;
        const resolutionEl = document.getElementById('resolution');
        if (resolutionEl) {
            resolutionEl.textContent = resolution;
        }
    }

    resizeCanvas() {
        if (!this.videoElement || !this.canvasElement) return;

        // Match canvas size to video display size
        const rect = this.videoElement.getBoundingClientRect();
        this.canvasElement.width = rect.width;
        this.canvasElement.height = rect.height;
        this.canvasElement.style.width = `${rect.width}px`;
        this.canvasElement.style.height = `${rect.height}px`;
    }

    handleResize() {
        this.resizeCanvas();
    }

    startFPSCounter() {
        let frameCount = 0;
        let lastTime = performance.now();
        
        this.fpsInterval = setInterval(() => {
            const currentTime = performance.now();
            const deltaTime = currentTime - lastTime;
            const fps = Math.round(1000 / deltaTime * frameCount);
            
            const fpsEl = document.getElementById('fps-counter');
            if (fpsEl) {
                fpsEl.textContent = fps;
            }
            
            frameCount = 0;
            lastTime = currentTime;
        }, 1000);
        
        // Count frames
        const countFrame = () => {
            frameCount++;
            if (this.fpsInterval) {
                requestAnimationFrame(countFrame);
            }
        };
        requestAnimationFrame(countFrame);
    }

    stopFPSCounter() {
        if (this.fpsInterval) {
            clearInterval(this.fpsInterval);
            this.fpsInterval = null;
        }
    }

    handleCameraError(error) {
        let message = 'Camera access failed';
        
        if (error.name === 'NotAllowedError') {
            message = 'Camera permission denied. Please allow camera access and refresh.';
        } else if (error.name === 'NotFoundError') {
            message = 'No camera found. Please connect a camera and refresh.';
        } else if (error.name === 'NotReadableError') {
            message = 'Camera is being used by another application.';
        }
        
        this.showVideoError(message);
    }

    showVideoError(message) {
        const videoWrapper = document.querySelector('.video-wrapper');
        if (videoWrapper) {
            videoWrapper.innerHTML = `
                <div class="video-error" style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    color: var(--text-secondary);
                    text-align: center;
                    padding: 40px;
                ">
                    <i class="fas fa-exclamation-triangle" style="
                        font-size: 48px;
                        color: var(--error-color);
                        margin-bottom: 20px;
                    "></i>
                    <h3 style="margin-bottom: 10px;">Camera Error</h3>
                    <p>${message}</p>
                    <button onclick="window.app.videoHandler.startCamera()" style="
                        margin-top: 20px;
                        padding: 10px 20px;
                        background: var(--primary-color);
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                    ">Retry</button>
                </div>
            `;
        }
    }

    showVideoPlaceholder(show) {
        const videoWrapper = document.querySelector('.video-wrapper');
        if (!videoWrapper) return;

        if (show) {
            videoWrapper.innerHTML = `
                <div class="video-placeholder" style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    color: var(--text-tertiary);
                    text-align: center;
                ">
                    <i class="fas fa-video" style="
                        font-size: 64px;
                        margin-bottom: 20px;
                        opacity: 0.5;
                    "></i>
                    <h3>No Video Feed</h3>
                    <p>Click "Start Detection" to begin camera feed</p>
                </div>
            `;
        }
    }

    updateConnectionStatus(connected) {
        // This will be called by the main app
        if (window.app) {
            window.app.updateConnectionStatus(connected);
        }
    }

    // Drawing detection overlays
    drawDetectionOverlay(detections) {
        if (!this.canvasContext || !this.canvasElement) return;

        // Clear previous overlays
        this.canvasContext.clearRect(0, 0, this.canvasElement.width, this.canvasElement.height);

        // Draw detection boxes and labels
        detections.forEach(detection => {
            this.drawDetectionBox(detection);
        });
    }

    drawDetectionBox(detection) {
        const { x, y, width, height, label, confidence } = detection;
        const ctx = this.canvasContext;

        // Draw bounding box
        ctx.strokeStyle = '#007AFF';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, width, height);

        // Draw label background
        const labelText = `${label} (${Math.round(confidence * 100)}%)`;
        const labelWidth = ctx.measureText(labelText).width + 10;
        const labelHeight = 20;

        ctx.fillStyle = 'rgba(0, 122, 255, 0.8)';
        ctx.fillRect(x, y - labelHeight, labelWidth, labelHeight);

        // Draw label text
        ctx.fillStyle = 'white';
        ctx.font = '12px Inter, sans-serif';
        ctx.fillText(labelText, x + 5, y - 5);
    }
}

// Export for use in other modules
window.VideoHandler = VideoHandler;
