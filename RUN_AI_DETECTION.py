#!/usr/bin/env python3
"""
AI Video Detection - Python Launcher
Starts the backend server and opens the web interface
"""

import os
import sys
import time
import threading
import webbrowser
import subprocess
from pathlib import Path

def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("🚀 AI VIDEO DETECTION SYSTEM")
    print("=" * 60)
    print("🧠 Advanced AI-powered video detection and analysis")
    print("📹 Real-time facial expression, age, and object detection")
    print("🖥️ Modern web-based interface")
    print("=" * 60)
    print()

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    # Check Python packages
    required_packages = ['flask', 'flask_cors', 'flask_socketio']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    print("✅ All dependencies are available")
    return True

def start_backend_server():
    """Start the Python backend server"""
    print("\n📡 Starting backend server...")
    
    # Path to the backend server
    server_path = Path(__file__).parent / "electron-frontend" / "backend-bridge" / "server.py"
    
    if not server_path.exists():
        print(f"❌ Backend server not found at: {server_path}")
        return None
    
    try:
        # Start the server in a separate process
        process = subprocess.Popen([
            sys.executable, str(server_path)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait a moment for the server to start
        time.sleep(3)
        
        # Check if the process is still running
        if process.poll() is None:
            print("✅ Backend server started successfully")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Backend server failed to start")
            print(f"Error: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Failed to start backend server: {e}")
        return None

def test_backend_connection():
    """Test if the backend server is responding"""
    print("\n🔌 Testing backend connection...")
    
    try:
        import urllib.request
        import json
        
        # Test the status endpoint
        with urllib.request.urlopen('http://localhost:8000/api/status', timeout=5) as response:
            data = json.loads(response.read().decode())
            
        if data.get('status') == 'online':
            print("✅ Backend connection successful")
            print(f"📊 Status: {data.get('status')}")
            print(f"🐍 Python backend: {data.get('python_backend')}")
            print(f"📅 Version: {data.get('version')}")
            return True
        else:
            print("❌ Backend not responding correctly")
            return False
            
    except Exception as e:
        print(f"❌ Backend connection failed: {e}")
        return False

def open_web_interface():
    """Open the web interface in the default browser"""
    print("\n🌐 Opening web interface...")
    
    # Path to the web frontend
    web_path = Path(__file__).parent / "web_frontend.html"
    
    if not web_path.exists():
        print(f"❌ Web interface not found at: {web_path}")
        return False
    
    try:
        # Open in default browser
        webbrowser.open(f"file://{web_path.absolute()}")
        print("✅ Web interface opened in browser")
        return True
    except Exception as e:
        print(f"❌ Failed to open web interface: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions"""
    print("\n" + "=" * 60)
    print("🎯 HOW TO USE YOUR AI VIDEO DETECTION SYSTEM")
    print("=" * 60)
    print()
    print("📱 In the browser window that opened:")
    print("   1. Click 'Start Camera' to access your webcam")
    print("   2. Click 'Start Detection' to begin AI processing")
    print("   3. Watch real-time results in the detection panels")
    print("   4. Use 'Snapshot' to capture images")
    print()
    print("🧠 AI Detection Features:")
    print("   • 😊 Facial Expression Detection")
    print("   • 👤 Age Detection (Custom Caffe Models)")
    print("   • 📦 Object Detection (YOLO)")
    print("   • 📊 Live Performance Statistics")
    print()
    print("🔧 Controls:")
    print("   • Green 'Start Detection' button to begin")
    print("   • Red 'Stop Detection' button to end")
    print("   • Camera button for snapshots")
    print("   • Real-time FPS and detection counters")
    print()
    print("=" * 60)

def main():
    """Main application launcher"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        input("Press Enter to exit...")
        return
    
    # Start backend server
    backend_process = start_backend_server()
    if not backend_process:
        print("\n❌ Failed to start backend server.")
        input("Press Enter to exit...")
        return
    
    # Test backend connection
    if not test_backend_connection():
        print("\n❌ Backend connection test failed.")
        if backend_process:
            backend_process.terminate()
        input("Press Enter to exit...")
        return
    
    # Open web interface
    if not open_web_interface():
        print("\n❌ Failed to open web interface.")
        if backend_process:
            backend_process.terminate()
        input("Press Enter to exit...")
        return
    
    # Show usage instructions
    show_usage_instructions()
    
    print("\n🎉 AI Video Detection System is now running!")
    print("\n⚠️ Keep this window open while using the application.")
    print("Press Ctrl+C or close this window to stop the system.")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down AI Video Detection System...")
        if backend_process:
            backend_process.terminate()
            print("✅ Backend server stopped")
        print("✅ System shutdown complete")

if __name__ == "__main__":
    main()
