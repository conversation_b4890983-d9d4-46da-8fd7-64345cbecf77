# 🚀 Complete Setup Guide - Electron Frontend for AI Video Detection

## 📋 Prerequisites Installation

### **Step 1: Install Node.js**

**Option A: Download from Official Website (Recommended)**
1. Go to https://nodejs.org/
2. Download the **LTS version** (Long Term Support)
3. Run the installer and follow the setup wizard
4. **Important**: Check "Add to PATH" during installation

**Option B: Using Package Manager (Windows)**
```powershell
# Using Chocolatey (if installed)
choco install nodejs

# Using Winget (Windows 10/11)
winget install OpenJS.NodeJS
```

**Verify Installation:**
```bash
node --version    # Should show v18.x.x or higher
npm --version     # Should show 9.x.x or higher
```

### **Step 2: Install Python Dependencies**
```bash
# Navigate to your project directory
cd "c:\Office365\OneDrive - UTHM\Desktop\2_AI220057_AI VIDEO DETECTION USING CONVOLUTIONAL NEURAL NETWORK TOOL"

# Install required Python packages
pip install flask flask-cors flask-socketio python-socketio eventlet
```

## 🏗️ Project Setup

### **Step 3: Navigate to Electron Frontend**
```bash
cd electron-frontend
```

### **Step 4: Install Node.js Dependencies**
```bash
# Install all required packages
npm install

# If npm install fails, try:
npm install --force
```

### **Step 5: Verify Directory Structure**
Your project should look like this:
```
electron-frontend/
├── main/
│   ├── main.js
│   └── preload.js
├── renderer/
│   ├── pages/
│   │   └── index.html
│   ├── css/
│   │   ├── main.css
│   │   └── components.css
│   └── js/
│       ├── app.js
│       ├── api-client.js
│       └── utils.js
├── backend-bridge/
│   └── server.py
└── package.json
```

## 🔧 Configuration

### **Step 6: Update Backend Server**
The backend server needs some adjustments for your existing system. Let me create an updated version:

**File: `backend-bridge/server.py`**
```python
#!/usr/bin/env python3
"""
AI Video Detection - Electron Backend Bridge
Updated for integration with existing system
"""

import sys
import os
import json
import time
import threading
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Web server imports
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ai-video-detection-secret-key'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Global state
detection_state = {
    "is_running": False,
    "current_detections": [],
    "statistics": {
        "total_detections": 0,
        "session_start": None,
        "fps": 0
    }
}

# Try to import your existing AI modules
try:
    # Try new structure first
    from app.main import main as app_main
    from interface.login.login_window import LoginWindow
    print("✅ Using new restructured imports")
    HAS_AI_MODULES = True
except ImportError:
    try:
        # Try legacy structure
        from main import main as app_main
        from gui.login_window import LoginWindow
        print("✅ Using legacy imports")
        HAS_AI_MODULES = True
    except ImportError:
        print("⚠️ AI modules not available, using mock mode")
        HAS_AI_MODULES = False

# Mock detector for testing
class MockDetector:
    def __init__(self, name):
        self.name = name
        self.is_running = False
        self.detection_count = 0
    
    def start(self):
        self.is_running = True
        return {"status": "started", "detector": self.name}
    
    def stop(self):
        self.is_running = False
        return {"status": "stopped", "detector": self.name}
    
    def get_results(self):
        if not self.is_running:
            return {"detections": [], "status": "stopped"}
        
        # Generate mock detection data
        import random
        detections = []
        if random.random() > 0.7:  # 30% chance of detection
            self.detection_count += 1
            detections.append({
                "id": self.detection_count,
                "label": f"{self.name} Detection {self.detection_count}",
                "confidence": random.uniform(0.7, 0.95),
                "timestamp": datetime.now().isoformat(),
                "details": f"Mock {self.name} detection result"
            })
        
        return {
            "detections": detections,
            "status": "running",
            "fps": random.randint(25, 35)
        }

# Initialize detectors
facial_detector = MockDetector("Facial Expression")
age_detector = MockDetector("Age")
object_detector = MockDetector("Object")

# API Routes
@app.route('/api/status', methods=['GET'])
def get_status():
    """Get system status"""
    return jsonify({
        "status": "online",
        "timestamp": datetime.now().isoformat(),
        "detection_running": detection_state["is_running"],
        "python_backend": "connected",
        "ai_modules": "available" if HAS_AI_MODULES else "mock_mode",
        "version": "1.0.0"
    })

@app.route('/api/detection/start', methods=['POST'])
def start_detection():
    """Start AI detection"""
    try:
        if detection_state["is_running"]:
            return jsonify({"error": "Detection already running"}), 400
        
        # Start detectors
        results = []
        results.append(facial_detector.start())
        results.append(age_detector.start())
        results.append(object_detector.start())
        
        detection_state["is_running"] = True
        detection_state["statistics"]["session_start"] = datetime.now().isoformat()
        
        # Emit to WebSocket clients
        socketio.emit('detection_started', {
            "timestamp": datetime.now().isoformat(),
            "results": results
        })
        
        logger.info("Detection started")
        return jsonify({
            "status": "started",
            "timestamp": datetime.now().isoformat(),
            "detectors": results
        })
        
    except Exception as e:
        logger.error(f"Error starting detection: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/detection/stop', methods=['POST'])
def stop_detection():
    """Stop AI detection"""
    try:
        if not detection_state["is_running"]:
            return jsonify({"error": "Detection not running"}), 400
        
        # Stop all detectors
        facial_detector.stop()
        age_detector.stop()
        object_detector.stop()
        
        detection_state["is_running"] = False
        
        # Emit to WebSocket clients
        socketio.emit('detection_stopped', {
            "timestamp": datetime.now().isoformat()
        })
        
        logger.info("Detection stopped")
        return jsonify({
            "status": "stopped",
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error stopping detection: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/detection/results', methods=['GET'])
def get_detection_results():
    """Get current detection results"""
    try:
        results = {
            "facial": facial_detector.get_results(),
            "age": age_detector.get_results(),
            "object": object_detector.get_results(),
            "timestamp": datetime.now().isoformat(),
            "is_running": detection_state["is_running"]
        }
        
        return jsonify(results)
        
    except Exception as e:
        logger.error(f"Error getting results: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/camera/test', methods=['POST'])
def test_camera():
    """Test camera connection"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            if ret:
                return jsonify({"status": "success", "message": "Camera is working"})
            else:
                return jsonify({"status": "error", "message": "Camera detected but no frame"}), 400
        else:
            return jsonify({"status": "error", "message": "No camera detected"}), 400
    except ImportError:
        return jsonify({"status": "error", "message": "OpenCV not available"}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# WebSocket events
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info('Client connected')
    emit('status', {
        "message": "Connected to AI Video Detection backend",
        "timestamp": datetime.now().isoformat()
    })

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info('Client disconnected')

@socketio.on('request_status')
def handle_status_request():
    """Handle status request"""
    emit('status_update', {
        "is_running": detection_state["is_running"],
        "timestamp": datetime.now().isoformat(),
        "statistics": detection_state["statistics"]
    })

# Background task for real-time updates
def background_task():
    """Send real-time detection updates"""
    while True:
        if detection_state["is_running"]:
            # Get current results
            results = {
                "facial": facial_detector.get_results(),
                "age": age_detector.get_results(),
                "object": object_detector.get_results(),
                "timestamp": datetime.now().isoformat()
            }
            
            # Update statistics
            total_detections = (
                len(results["facial"].get("detections", [])) +
                len(results["age"].get("detections", [])) +
                len(results["object"].get("detections", []))
            )
            detection_state["statistics"]["total_detections"] += total_detections
            
            # Emit to all connected clients
            socketio.emit('detection_update', results)
        
        time.sleep(0.5)  # 2 FPS updates to avoid overwhelming

# Start background task
def start_background_task():
    """Start the background task in a separate thread"""
    thread = threading.Thread(target=background_task)
    thread.daemon = True
    thread.start()

if __name__ == '__main__':
    print("🚀 Starting AI Video Detection Backend Bridge...")
    print("📡 Server: http://localhost:8000")
    print("🔌 WebSocket: ws://localhost:8000")
    print("=" * 50)
    
    # Start background task
    start_background_task()
    
    # Run the server
    try:
        socketio.run(app, host='localhost', port=8000, debug=False, allow_unsafe_werkzeug=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
```

## 🚀 Running the Application

### **Step 7: Start the Backend Server**
```bash
# Navigate to project root
cd "c:\Office365\OneDrive - UTHM\Desktop\2_AI220057_AI VIDEO DETECTION USING CONVOLUTIONAL NEURAL NETWORK TOOL"

# Start the Python backend
python electron-frontend/backend-bridge/server.py
```

You should see:
```
🚀 Starting AI Video Detection Backend Bridge...
📡 Server: http://localhost:8000
🔌 WebSocket: ws://localhost:8000
==================================================
 * Running on http://localhost:8000
```

### **Step 8: Start the Electron Frontend**
Open a **new terminal** and run:
```bash
# Navigate to electron frontend
cd "c:\Office365\OneDrive - UTHM\Desktop\2_AI220057_AI VIDEO DETECTION USING CONVOLUTIONAL NEURAL NETWORK TOOL\electron-frontend"

# Start Electron app
npm start
```

## 🔧 Troubleshooting

### **Common Issues:**

**1. Node.js not found**
- Install Node.js from https://nodejs.org/
- Restart your terminal after installation

**2. npm install fails**
```bash
# Clear cache and try again
npm cache clean --force
npm install
```

**3. Python modules not found**
```bash
# Install missing packages
pip install flask flask-cors flask-socketio
```

**4. Port already in use**
```bash
# Kill process using port 8000
netstat -ano | findstr :8000
taskkill /PID <PID_NUMBER> /F
```

**5. Electron won't start**
```bash
# Rebuild electron
npm run postinstall
```

## ✅ Success Indicators

When everything is working correctly, you should see:

1. **Backend Terminal**: Server running messages
2. **Electron App**: Desktop application opens
3. **Connection Status**: Green dot showing "Connected"
4. **Video Feed**: Camera access (if available)
5. **Detection Panels**: Ready to show results

## 🎯 Next Steps

Once running:
1. Click "Start Detection" to begin AI processing
2. Test camera functionality
3. View real-time detection results
4. Explore the dashboard and settings

The application will work in **mock mode** initially, showing sample detection data. Once your AI modules are properly integrated, it will show real detection results!
