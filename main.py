import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# This file will be moved to app/main.py
# Keeping original for backward compatibility

def create_directory_structure():
    """Create necessary directories if they don't exist"""
    directories = [
        'gui',
        'utils', 
        'detection',
        'recording',
        'models',
        'logs',
        'reports',
        'snapshots',
        'Security_Footage'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        
        # Create __init__.py files for Python packages
        if directory in ['gui', 'utils', 'detection', 'recording']:
            init_file = os.path.join(directory, '__init__.py')
            if not os.path.exists(init_file):
                with open(init_file, 'w') as f:
                    f.write('# Package initialization\n')

def check_dependencies():
    """Check and report on available dependencies"""
    dependencies = {
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'pillow': 'PIL',
        'tkinter': 'tkinter',
        'datetime': 'datetime'
    }
    
    available = []
    missing = []
    
    for package, module in dependencies.items():
        try:
            __import__(module)
            available.append(package)
        except ImportError:
            missing.append(package)
    
    print(f"✅ Available dependencies: {', '.join(available)}")
    if missing:
        print(f"⚠️ Missing dependencies: {', '.join(missing)}")
        print("Install missing packages with: pip install " + " ".join(missing))
    
    return len(missing) == 0

def main():
    """Main application entry point"""
    try:
        print("Starting AI Video Detection Tool...")
        
        # Create directory structure
        create_directory_structure()
        
        # Check dependencies
        deps_ok = check_dependencies()
        if not deps_ok:
            print("⚠️ Some dependencies are missing, but the app will try to run...")
        
        # Import after directory creation to avoid import errors
        try:
            from gui.login_window import LoginWindow
            print("✅ Login window module imported successfully")
        except ImportError as e:
            print(f"❌ Error importing login window: {e}")
            print("🔄 Creating basic login interface...")
            create_basic_interface()
            return
            
        # Start login window
        app = LoginWindow()
        app.run()
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        create_emergency_interface()

def create_basic_interface():
    """Create a basic interface when imports fail"""
    root = tk.Tk()
    root.title("AI Video Detection")
    root.geometry("600x400")
    
    # Main frame
    main_frame = tk.Frame(root, bg='#E8F4FD')
    main_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # Title
    title_label = tk.Label(main_frame, text="🛡️ AI Video Detection",
                          font=('Arial', 18, 'bold'),
                          bg='#E8F4FD', fg='#2C3E50')
    title_label.pack(pady=20)
    
    # Status
    status_label = tk.Label(main_frame, text="⚠️ Tool in Basic Mode\nSome features may be limited",
                           font=('Arial', 12),
                           bg='#E8F4FD', fg='#E67E22')
    status_label.pack(pady=10)
    
    # Camera button
    def start_camera():
        try:
            import cv2
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                messagebox.showinfo("Success", "✅ Camera detected and working!")
                cap.release()
            else:
                messagebox.showerror("Error", "❌ No camera detected")
        except ImportError:
            messagebox.showerror("Error", "❌ OpenCV not available")
    
    camera_btn = tk.Button(main_frame, text="📷 Test Camera",
                          font=('Arial', 12, 'bold'),
                          bg='#3498DB', fg='white',
                          command=start_camera,
                          pady=10, padx=20)
    camera_btn.pack(pady=10)
    
    # Info
    info_text = """
🎯 AI Video Detection Features:
• Real-time facial expression detection
• Age estimation and analysis  
• Object detection and tracking
• Anomaly and security monitoring
• Comprehensive reporting system

📋 System Status:
• Core system: Operational
• Camera support: Testing available
• AI models: Loading...
• Database: Initializing...

🔧 To access full features:
1. Install dependencies: pip install opencv-python numpy pillow
2. Download AI models: python download_models.py
3. Restart application
"""
    
    info_label = tk.Label(main_frame, text=info_text,
                         font=('Arial', 10),
                         bg='#E8F4FD', fg='#2C3E50',
                         justify='left')
    info_label.pack(pady=20)
    
    # Exit button
    exit_btn = tk.Button(main_frame, text="🚪 Exit",
                        font=('Arial', 12, 'bold'),
                        bg='#E74C3C', fg='white',
                        command=root.destroy,
                        pady=8, padx=20)
    exit_btn.pack(pady=10)
    
    root.mainloop()

def create_emergency_interface():
    """Emergency interface when everything fails"""
    try:
        root = tk.Tk()
        root.title("AI Video Detection - Emergency Mode")
        root.geometry("400x300")
        
        label = tk.Label(root, text="⚠️ AI Video Detection Tool\nEmergency Mode",
                        font=('Arial', 16, 'bold'), fg='red')
        label.pack(expand=True)
        
        info = tk.Label(root, text="Please check:\n1. Python installation\n2. Required packages\n3. File permissions")
        info.pack(expand=True)
        
        tk.Button(root, text="Exit", command=root.destroy).pack(pady=20)
        
        root.mainloop()
    except:
        print("❌ Critical error: Cannot create emergency interface")
        print("Please check your Python and tkinter installation")

if __name__ == "__main__":
    main()