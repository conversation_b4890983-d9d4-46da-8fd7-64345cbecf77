#!/usr/bin/env python3
"""
AI Video Detection - Enhanced Application Launcher
Restructured version with improved error handling and Unicode support
"""

import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

def print_safe(text, fallback_text=None):
    """Print text with Unicode error handling"""
    try:
        print(text)
    except UnicodeEncodeError:
        if fallback_text:
            print(fallback_text)
        else:
            # Remove emojis and special characters
            safe_text = text.encode('ascii', 'ignore').decode('ascii')
            print(safe_text)

def main():
    """Main entry point for the enhanced application"""
    print_safe("🛡️ AI Video Detection - iOS Enhanced Interface", 
               "AI Video Detection - iOS Enhanced Interface")
    print("=" * 55)
    print()
    
    print_safe("🎨 Features:", "Features:")
    print_safe("   ✅ iOS-inspired blue theme (#007AFF)", 
               "   - iOS-inspired blue theme (#007AFF)")
    print_safe("   ✅ Rounded corners and modern typography", 
               "   - Rounded corners and modern typography")
    print_safe("   ✅ Card-based layout with shadows", 
               "   - Card-based layout with shadows")
    print_safe("   ✅ Animated buttons and status indicators", 
               "   - Animated buttons and status indicators")
    print_safe("   ✅ Enhanced AI detection accuracy", 
               "   - Enhanced AI detection accuracy")
    print_safe("   ✅ All original functionality preserved", 
               "   - All original functionality preserved")
    print()
    
    print_safe("🚀 Starting application...", "Starting application...")
    print()
    
    try:
        # Try new structure first
        try:
            from interface.main_window.main_window import MainWindow
            print("✅ Main window imported from new structure")
        except ImportError:
            # Fallback to old structure
            from gui.main_window import MainWindow
            print("✅ Main window imported from legacy structure")
        
        app = MainWindow()
        app.run()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure all dependencies are installed:")
        print("   pip install opencv-python pillow numpy")
        print("2. Check camera permissions")
        print("3. Close other camera applications")
        print("4. Try running: python app/main.py")

if __name__ == "__main__":
    main()
