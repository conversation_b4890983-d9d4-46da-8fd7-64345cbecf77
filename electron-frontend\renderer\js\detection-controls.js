/**
 * AI Video Detection - Detection Controls
 * Handles detection start/stop, settings, and control interactions
 */

class DetectionControls {
    constructor() {
        this.isDetectionRunning = false;
        this.isRecording = false;
        this.apiClient = null;
        this.eventListeners = new Map();
        
        // Control elements
        this.startBtn = null;
        this.stopBtn = null;
        this.snapshotBtn = null;
        this.recordBtn = null;
        
        // Detection toggles
        this.facialToggle = null;
        this.ageToggle = null;
        this.objectToggle = null;
        
        // Bind methods
        this.init = this.init.bind(this);
        this.startDetection = this.startDetection.bind(this);
        this.stopDetection = this.stopDetection.bind(this);
        this.takeSnapshot = this.takeSnapshot.bind(this);
        this.toggleRecording = this.toggleRecording.bind(this);
    }

    async init() {
        try {
            console.log('🎛️ Initializing detection controls...');
            
            // Get API client reference
            this.apiClient = window.app?.apiClient;
            if (!this.apiClient) {
                throw new Error('API client not available');
            }
            
            // Get control elements
            this.getControlElements();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Setup detection toggles
            this.setupDetectionToggles();
            
            // Initialize UI state
            this.updateControlsState();
            
            console.log('✅ Detection controls initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize detection controls:', error);
            throw error;
        }
    }

    getControlElements() {
        this.startBtn = document.getElementById('start-detection');
        this.stopBtn = document.getElementById('stop-detection');
        this.snapshotBtn = document.getElementById('take-snapshot');
        this.recordBtn = document.getElementById('start-recording');
        
        this.facialToggle = document.getElementById('facial-toggle');
        this.ageToggle = document.getElementById('age-toggle');
        this.objectToggle = document.getElementById('object-toggle');
        
        // Verify required elements exist
        if (!this.startBtn || !this.stopBtn) {
            throw new Error('Required control buttons not found');
        }
    }

    setupEventListeners() {
        // Start detection button
        if (this.startBtn) {
            this.startBtn.addEventListener('click', this.startDetection);
        }
        
        // Stop detection button
        if (this.stopBtn) {
            this.stopBtn.addEventListener('click', this.stopDetection);
        }
        
        // Snapshot button
        if (this.snapshotBtn) {
            this.snapshotBtn.addEventListener('click', this.takeSnapshot);
        }
        
        // Recording button
        if (this.recordBtn) {
            this.recordBtn.addEventListener('click', this.toggleRecording);
        }
    }

    setupDetectionToggles() {
        const toggles = [
            { element: this.facialToggle, type: 'facial' },
            { element: this.ageToggle, type: 'age' },
            { element: this.objectToggle, type: 'object' }
        ];
        
        toggles.forEach(({ element, type }) => {
            if (element) {
                element.addEventListener('change', (e) => {
                    this.onDetectionToggle(type, e.target.checked);
                });
            }
        });
    }

    async startDetection() {
        try {
            if (this.isDetectionRunning) {
                console.log('⚠️ Detection already running');
                return;
            }

            console.log('🚀 Starting detection...');
            
            // Update UI immediately
            this.setDetectionState(true);
            
            // Start camera if not already running
            if (window.app?.videoHandler) {
                await window.app.videoHandler.startCamera();
            }
            
            // Send start request to backend
            const response = await this.apiClient.startDetection();
            console.log('✅ Detection started:', response);
            
            // Emit event
            this.emit('detectionStarted', response);
            
            // Show notification
            if (window.notificationSystem) {
                window.notificationSystem.success('AI Detection started successfully');
            }
            
        } catch (error) {
            console.error('❌ Failed to start detection:', error);
            
            // Revert UI state
            this.setDetectionState(false);
            
            // Show error notification
            if (window.notificationSystem) {
                window.notificationSystem.error(`Failed to start detection: ${error.message}`);
            }
            
            throw error;
        }
    }

    async stopDetection() {
        try {
            if (!this.isDetectionRunning) {
                console.log('⚠️ Detection not running');
                return;
            }

            console.log('🛑 Stopping detection...');
            
            // Update UI immediately
            this.setDetectionState(false);
            
            // Send stop request to backend
            const response = await this.apiClient.stopDetection();
            console.log('✅ Detection stopped:', response);
            
            // Stop recording if active
            if (this.isRecording) {
                this.stopRecording();
            }
            
            // Emit event
            this.emit('detectionStopped', response);
            
            // Show notification
            if (window.notificationSystem) {
                window.notificationSystem.info('AI Detection stopped');
            }
            
        } catch (error) {
            console.error('❌ Failed to stop detection:', error);
            
            // Show error notification
            if (window.notificationSystem) {
                window.notificationSystem.error(`Failed to stop detection: ${error.message}`);
            }
            
            throw error;
        }
    }

    async takeSnapshot() {
        try {
            console.log('📸 Taking snapshot...');
            
            // Take snapshot using video handler
            if (window.app?.videoHandler) {
                const filename = await window.app.videoHandler.takeSnapshot();
                
                // Emit event
                this.emit('snapshotTaken', filename);
                
                // Show notification
                if (window.notificationSystem) {
                    window.notificationSystem.success(`Snapshot saved: ${filename}`);
                }
                
                return filename;
            } else {
                throw new Error('Video handler not available');
            }
            
        } catch (error) {
            console.error('❌ Failed to take snapshot:', error);
            
            // Show error notification
            if (window.notificationSystem) {
                window.notificationSystem.error(`Failed to take snapshot: ${error.message}`);
            }
            
            throw error;
        }
    }

    async toggleRecording() {
        if (this.isRecording) {
            await this.stopRecording();
        } else {
            await this.startRecording();
        }
    }

    async startRecording() {
        try {
            if (this.isRecording) {
                console.log('⚠️ Already recording');
                return;
            }

            console.log('🎥 Starting recording...');
            
            // Start recording using video handler
            if (window.app?.videoHandler) {
                await window.app.videoHandler.startRecording();
                
                this.isRecording = true;
                this.updateRecordingButton();
                
                // Show notification
                if (window.notificationSystem) {
                    window.notificationSystem.success('Recording started');
                }
                
            } else {
                throw new Error('Video handler not available');
            }
            
        } catch (error) {
            console.error('❌ Failed to start recording:', error);
            
            // Show error notification
            if (window.notificationSystem) {
                window.notificationSystem.error(`Failed to start recording: ${error.message}`);
            }
            
            throw error;
        }
    }

    async stopRecording() {
        try {
            if (!this.isRecording) {
                console.log('⚠️ Not recording');
                return;
            }

            console.log('🛑 Stopping recording...');
            
            // Stop recording using video handler
            if (window.app?.videoHandler) {
                window.app.videoHandler.stopRecording();
                
                this.isRecording = false;
                this.updateRecordingButton();
                
                // Show notification
                if (window.notificationSystem) {
                    window.notificationSystem.success('Recording stopped and saved');
                }
            }
            
        } catch (error) {
            console.error('❌ Failed to stop recording:', error);
            
            // Show error notification
            if (window.notificationSystem) {
                window.notificationSystem.error(`Failed to stop recording: ${error.message}`);
            }
        }
    }

    onDetectionToggle(type, enabled) {
        console.log(`🔄 ${type} detection ${enabled ? 'enabled' : 'disabled'}`);
        
        // Update settings via API
        if (this.apiClient) {
            const settings = {};
            settings[`enable_${type}_detection`] = enabled;
            
            this.apiClient.updateSettings(settings).catch(error => {
                console.error(`Failed to update ${type} detection setting:`, error);
                
                // Revert toggle state
                const toggle = document.getElementById(`${type}-toggle`);
                if (toggle) {
                    toggle.checked = !enabled;
                }
            });
        }
        
        // Update panel visibility
        this.updatePanelVisibility(type, enabled);
    }

    updatePanelVisibility(type, enabled) {
        const panel = document.querySelector(`#${type}-results`).closest('.detection-panel');
        if (panel) {
            panel.style.opacity = enabled ? '1' : '0.5';
            panel.style.pointerEvents = enabled ? 'auto' : 'none';
        }
    }

    setDetectionState(running) {
        this.isDetectionRunning = running;
        this.updateControlsState();
    }

    updateControlsState() {
        // Update button states
        if (this.startBtn) {
            this.startBtn.disabled = this.isDetectionRunning;
            this.startBtn.style.opacity = this.isDetectionRunning ? '0.5' : '1';
        }
        
        if (this.stopBtn) {
            this.stopBtn.disabled = !this.isDetectionRunning;
            this.stopBtn.style.opacity = !this.isDetectionRunning ? '0.5' : '1';
        }
        
        // Update button text/icons
        this.updateButtonContent();
    }

    updateButtonContent() {
        if (this.startBtn) {
            const icon = this.startBtn.querySelector('i');
            const text = this.startBtn.querySelector('span');
            
            if (this.isDetectionRunning) {
                if (icon) icon.className = 'fas fa-spinner fa-spin';
                if (text) text.textContent = 'Running...';
            } else {
                if (icon) icon.className = 'fas fa-play';
                if (text) text.textContent = 'Start Detection';
            }
        }
    }

    updateRecordingButton() {
        if (this.recordBtn) {
            const icon = this.recordBtn.querySelector('i');
            const text = this.recordBtn.querySelector('span');
            
            if (this.isRecording) {
                if (icon) icon.className = 'fas fa-stop';
                if (text) text.textContent = 'Stop Recording';
                this.recordBtn.style.background = 'var(--error-color)';
            } else {
                if (icon) icon.className = 'fas fa-record-vinyl';
                if (text) text.textContent = 'Record';
                this.recordBtn.style.background = '';
            }
        }
    }

    // Event system
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }

    // Keyboard shortcuts
    handleKeyboardShortcut(key) {
        switch (key) {
            case 'F5':
                this.startDetection();
                break;
            case 'F6':
                this.stopDetection();
                break;
            case 'F12':
                this.takeSnapshot();
                break;
            default:
                return false;
        }
        return true;
    }

    // Get current state
    getState() {
        return {
            isDetectionRunning: this.isDetectionRunning,
            isRecording: this.isRecording,
            detectionSettings: {
                facial: this.facialToggle?.checked || false,
                age: this.ageToggle?.checked || false,
                object: this.objectToggle?.checked || false
            }
        };
    }

    // Reset to initial state
    reset() {
        this.isDetectionRunning = false;
        this.isRecording = false;
        this.updateControlsState();
        this.updateRecordingButton();
    }
}

// Export for use in other modules
window.DetectionControls = DetectionControls;
