#!/usr/bin/env python3
"""
AI Video Detection - Electron Backend Bridge
HTTP and WebSocket server for communication between Electron frontend and Python backend
"""

import sys
import os
import json
import asyncio
import threading
import time
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Web server imports
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import logging

# Mock detector class for development and fallback
class MockDetector:
    def __init__(self, name="Mock"):
        self.name = name
        self.is_running = False
        self.detection_count = 0

    def start(self):
        self.is_running = True
        return {"status": "started", "detector": self.name}

    def stop(self):
        self.is_running = False
        return {"status": "stopped", "detector": self.name}

    def get_results(self):
        if not self.is_running:
            return {"detections": [], "status": "stopped"}

        # Generate mock detection data
        import random
        detections = []
        if random.random() > 0.7:  # 30% chance of detection
            self.detection_count += 1
            detections.append({
                "id": self.detection_count,
                "label": f"{self.name} Detection {self.detection_count}",
                "confidence": random.uniform(0.7, 0.95),
                "timestamp": datetime.now().isoformat(),
                "details": f"Mock {self.name} detection result"
            })

        return {
            "detections": detections,
            "status": "running",
            "fps": random.randint(25, 35)
        }

# AI Detection imports (with fallbacks)
HAS_AI_MODULES = False
try:
    from interface.login.login_window import LoginWindow
    from interface.main_window.main_window import MainWindow
    from ai_detection.facial.expression_detector import *
    from ai_detection.facial.age_detector import *
    from ai_detection.object.object_detector import *
    from system.config.app_config import Config
    print("✅ Using new restructured imports")
    HAS_AI_MODULES = True
except ImportError:
    try:
        from gui.login_window import LoginWindow
        from gui.main_window import MainWindow
        from detection.facial_expression import *
        from detection.age_detection import *
        from detection.object_detection import *
        from utils.config import Config
        print("✅ Using legacy imports")
        HAS_AI_MODULES = True
    except ImportError as e:
        print(f"⚠️ Import error: {e}")
        print("Using mock mode for development...")

        class Config:
            DEFAULT_USERNAME = "admin"
            DEFAULT_PASSWORD = "password123"

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ai-video-detection-secret-key'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Global state
detection_state = {
    "is_running": False,
    "current_detections": [],
    "statistics": {
        "total_detections": 0,
        "session_start": None,
        "fps": 0
    },
    "settings": {
        "detection_sensitivity": 0.5,
        "enable_facial_detection": True,
        "enable_age_detection": True,
        "enable_object_detection": True,
        "enable_anomaly_detection": False
    }
}

# Initialize detectors
if HAS_AI_MODULES:
    try:
        # Try to use actual AI detectors
        facial_detector = ExpressionDetector() if 'ExpressionDetector' in globals() else MockDetector("Facial Expression")
        age_detector = AgeDetector() if 'AgeDetector' in globals() else MockDetector("Age")
        object_detector = ObjectDetector() if 'ObjectDetector' in globals() else MockDetector("Object")
        print("✅ AI detectors initialized")
    except Exception as e:
        print(f"⚠️ Failed to initialize AI detectors: {e}")
        facial_detector = MockDetector("Facial Expression")
        age_detector = MockDetector("Age")
        object_detector = MockDetector("Object")
else:
    # Use mock detectors
    facial_detector = MockDetector("Facial Expression")
    age_detector = MockDetector("Age")
    object_detector = MockDetector("Object")
    print("✅ Mock detectors initialized")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API Routes
@app.route('/api/status', methods=['GET'])
def get_status():
    """Get system status"""
    return jsonify({
        "status": "online",
        "timestamp": datetime.now().isoformat(),
        "detection_running": detection_state["is_running"],
        "python_backend": "connected",
        "version": "1.0.0"
    })

@app.route('/api/detection/start', methods=['POST'])
def start_detection():
    """Start AI detection"""
    try:
        if detection_state["is_running"]:
            return jsonify({"error": "Detection already running"}), 400
        
        # Start detectors based on settings
        results = []
        if detection_state["settings"]["enable_facial_detection"]:
            result = facial_detector.start()
            results.append(result)
        
        if detection_state["settings"]["enable_age_detection"]:
            result = age_detector.start()
            results.append(result)
        
        if detection_state["settings"]["enable_object_detection"]:
            result = object_detector.start()
            results.append(result)
        
        detection_state["is_running"] = True
        detection_state["statistics"]["session_start"] = datetime.now().isoformat()
        
        # Emit to WebSocket clients
        socketio.emit('detection_started', {
            "timestamp": datetime.now().isoformat(),
            "results": results
        })
        
        return jsonify({
            "status": "started",
            "timestamp": datetime.now().isoformat(),
            "detectors": results
        })
        
    except Exception as e:
        logger.error(f"Error starting detection: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/detection/stop', methods=['POST'])
def stop_detection():
    """Stop AI detection"""
    try:
        if not detection_state["is_running"]:
            return jsonify({"error": "Detection not running"}), 400
        
        # Stop all detectors
        facial_detector.stop()
        age_detector.stop()
        object_detector.stop()
        
        detection_state["is_running"] = False
        
        # Emit to WebSocket clients
        socketio.emit('detection_stopped', {
            "timestamp": datetime.now().isoformat()
        })
        
        return jsonify({
            "status": "stopped",
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error stopping detection: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/detection/results', methods=['GET'])
def get_detection_results():
    """Get current detection results"""
    try:
        results = {
            "facial": facial_detector.get_results(),
            "age": age_detector.get_results(),
            "object": object_detector.get_results(),
            "timestamp": datetime.now().isoformat(),
            "is_running": detection_state["is_running"]
        }
        
        return jsonify(results)
        
    except Exception as e:
        logger.error(f"Error getting results: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/settings', methods=['GET'])
def get_settings():
    """Get current settings"""
    return jsonify(detection_state["settings"])

@app.route('/api/settings', methods=['POST'])
def update_settings():
    """Update settings"""
    try:
        new_settings = request.get_json()
        detection_state["settings"].update(new_settings)
        
        # Emit to WebSocket clients
        socketio.emit('settings_updated', detection_state["settings"])
        
        return jsonify({
            "status": "updated",
            "settings": detection_state["settings"]
        })
        
    except Exception as e:
        logger.error(f"Error updating settings: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """Get detection statistics"""
    return jsonify(detection_state["statistics"])

@app.route('/api/camera/test', methods=['POST'])
def test_camera():
    """Test camera connection"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            cap.release()
            return jsonify({"status": "success", "message": "Camera is working"})
        else:
            return jsonify({"status": "error", "message": "No camera detected"}), 400
    except ImportError:
        return jsonify({"status": "error", "message": "OpenCV not available"}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# WebSocket events
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info('Client connected')
    emit('status', {
        "message": "Connected to AI Video Detection backend",
        "timestamp": datetime.now().isoformat()
    })

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info('Client disconnected')

@socketio.on('request_status')
def handle_status_request():
    """Handle status request"""
    emit('status_update', {
        "is_running": detection_state["is_running"],
        "timestamp": datetime.now().isoformat(),
        "statistics": detection_state["statistics"]
    })

# Background task for real-time updates
def background_task():
    """Send real-time detection updates"""
    while True:
        if detection_state["is_running"]:
            # Get current results
            results = {
                "facial": facial_detector.get_results(),
                "age": age_detector.get_results(),
                "object": object_detector.get_results(),
                "timestamp": datetime.now().isoformat()
            }
            
            # Emit to all connected clients
            socketio.emit('detection_update', results)
            
            # Update statistics
            detection_state["statistics"]["total_detections"] += len(results.get("facial", {}).get("detections", []))
        
        time.sleep(0.1)  # 10 FPS updates

# Start background task
def start_background_task():
    """Start the background task in a separate thread"""
    thread = threading.Thread(target=background_task)
    thread.daemon = True
    thread.start()

if __name__ == '__main__':
    print("🚀 Starting AI Video Detection Backend Bridge...")
    print("📡 HTTP Server: http://localhost:8000")
    print("🔌 WebSocket Server: ws://localhost:8001")
    print("=" * 50)
    
    # Start background task
    start_background_task()
    
    # Run the server
    try:
        socketio.run(app, host='localhost', port=8000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
