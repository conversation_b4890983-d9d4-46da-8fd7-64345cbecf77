@echo off
title AI Video Detection - Quick Setup
color 0A

echo.
echo ========================================
echo    AI Video Detection - Quick Setup
echo ========================================
echo.

REM Check if Node.js is installed
echo [1/4] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Node.js is NOT installed
    echo.
    echo 📥 INSTALLING NODE.JS AUTOMATICALLY...
    echo.
    
    REM Try to download and install Node.js using winget (Windows 10/11)
    echo Attempting automatic installation with winget...
    winget install OpenJS.NodeJS --silent --accept-package-agreements --accept-source-agreements >nul 2>&1
    
    if %errorlevel% equ 0 (
        echo ✅ Node.js installed successfully!
        echo 🔄 Please restart this script to continue...
        echo.
        pause
        exit /b 0
    ) else (
        echo.
        echo ⚠️ Automatic installation failed. Manual installation required.
        echo.
        echo 📋 MANUAL INSTALLATION STEPS:
        echo 1. Go to: https://nodejs.org/
        echo 2. Download the LTS version
        echo 3. Run the installer
        echo 4. Restart your computer
        echo 5. Run this script again
        echo.
        echo 🌐 Opening Node.js download page...
        start https://nodejs.org/
        pause
        exit /b 1
    )
) else (
    echo ✅ Node.js is installed
    node --version
)

echo.
echo [2/4] Checking Python backend...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not available
    pause
    exit /b 1
) else (
    echo ✅ Python is available
    python --version
)

echo.
echo [3/4] Installing dependencies...
cd electron-frontend

if not exist "package.json" (
    echo ❌ package.json not found!
    echo Make sure you're in the correct directory.
    pause
    exit /b 1
)

echo 📦 Installing Node.js packages...
call npm install --silent
if %errorlevel% neq 0 (
    echo ⚠️ Standard install failed, trying with --force...
    call npm install --force --silent
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

echo ✅ Dependencies installed successfully!

echo.
echo [4/4] Starting the application...
echo.
echo 🚀 LAUNCHING AI VIDEO DETECTION APP...
echo.
echo This will open:
echo 1. Backend Server (Python) - Keep running
echo 2. Electron App (Desktop) - Your main application
echo.

REM Start backend server in new window
echo 📡 Starting Python backend...
cd ..
start "AI Detection Backend" cmd /k "python electron-frontend/backend-bridge/server.py"

REM Wait for backend to start
echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

REM Start Electron frontend
echo 🖥️ Starting Electron frontend...
cd electron-frontend
start "AI Detection Frontend" cmd /k "npm start"

echo.
echo 🎉 APPLICATION LAUNCHED!
echo.
echo You should now see:
echo ✅ Backend window with Python server running
echo ✅ Electron desktop application opening
echo.
echo If you see any errors, check both windows for details.
echo.
pause
