@echo off
echo ========================================
echo   AI Video Detection - Electron App
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Download the LTS version and restart this script.
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed
node --version

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed!
    echo.
    echo Please install Python from: https://python.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Python is installed
python --version

echo.
echo 📦 Installing dependencies...
echo.

REM Navigate to electron frontend directory
cd electron-frontend

REM Check if package.json exists
if not exist "package.json" (
    echo ❌ package.json not found!
    echo Make sure you're running this from the project root directory.
    pause
    exit /b 1
)

REM Install Node.js dependencies
echo 📦 Installing Node.js dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install Node.js dependencies!
    echo.
    echo Trying with --force flag...
    call npm install --force
    if %errorlevel% neq 0 (
        echo ❌ Still failed! Please check your internet connection.
        pause
        exit /b 1
    )
)

echo ✅ Node.js dependencies installed

REM Install Python dependencies
echo.
echo 📦 Installing Python dependencies...
cd ..
pip install flask flask-cors flask-socketio python-socketio eventlet
if %errorlevel% neq 0 (
    echo ⚠️ Some Python packages may have failed to install
    echo The app will still work in mock mode
)

echo ✅ Python dependencies installed

echo.
echo 🚀 Starting AI Video Detection App...
echo.
echo This will open two windows:
echo 1. Backend Server (Python) - Keep this running
echo 2. Electron App (Desktop) - Your main application
echo.

REM Start backend server in a new window
echo 📡 Starting backend server...
start "AI Detection Backend" cmd /k "cd /d "%cd%" && python electron-frontend/backend-bridge/server.py"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start Electron frontend
echo 🖥️ Starting Electron frontend...
cd electron-frontend
call npm start

echo.
echo 🎉 Application started successfully!
echo.
echo If you see any errors:
echo 1. Make sure both windows stay open
echo 2. Check the backend window for Python errors
echo 3. Check the Electron window for frontend errors
echo.
pause
